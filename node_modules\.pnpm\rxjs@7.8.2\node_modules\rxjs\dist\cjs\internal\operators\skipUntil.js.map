{"version": 3, "file": "skipUntil.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/skipUntil.ts"], "names": [], "mappings": ";;;AACA,qCAAuC;AACvC,2DAAgE;AAChE,qDAAoD;AACpD,qCAAoC;AA+CpC,SAAgB,SAAS,CAAI,QAA8B;IACzD,OAAO,cAAO,CAAC,UAAC,MAAM,EAAE,UAAU;QAChC,IAAI,MAAM,GAAG,KAAK,CAAC;QAEnB,IAAM,cAAc,GAAG,6CAAwB,CAC7C,UAAU,EACV;YACE,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,WAAW,EAAE,CAAC;YAC9B,MAAM,GAAG,IAAI,CAAC;QAChB,CAAC,EACD,WAAI,CACL,CAAC;QAEF,qBAAS,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QAE9C,MAAM,CAAC,SAAS,CAAC,6CAAwB,CAAC,UAAU,EAAE,UAAC,KAAK,IAAK,OAAA,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAhC,CAAgC,CAAC,CAAC,CAAC;IACtG,CAAC,CAAC,CAAC;AACL,CAAC;AAjBD,8BAiBC"}