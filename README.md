# valtab

Sync your Valtio state across browser tabs.

```sh
npm install valtab
```

## Basic Usage with React Hook

Define the Valtio store to share in your `store.js`

```tsx
import { proxy } from 'valtio'

const state = proxy({
  $id: 'shared-valtio-state', // Add a unique `$id` field here
  count: 0,
})
```

Call `useSharedValtioState` and then consume the store all the same in your `Component.tsx`

```tsx
import { useSnapshot } from 'valtio'
import { useSharedValtioState } from 'valtab'

function MyComponent() {
  // Share the entire state across tabs
  useSharedValtioState(state, {
   // Get initial state from other tabs
    initialize: true
  })
  // Everything is the same as valtio then
  const snap = useSnapshot(state)
  
  return (
    <div>
      <p>Count: {snap.count}</p>
      <button onClick={() => state.count++}>Increment</button>
    </div>
  )
}
```

## How It Works

- [BroadcastChannel](https://developer.mozilla.org/en-US/docs/Web/API/BroadcastChannel) is used to communicate between browser tabs
- Conflict resolution is timestamp-based, latest wins
- State should be [superjson](https://github.com/flightcontrolhq/superjson) serializable

## License

MIT
