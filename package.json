{"name": "valtab", "version": "1.0.0", "description": "Sync your Valtio state across browser tabs", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "scripts": {"build": "tsup && node post-build.js"}, "keywords": ["valtio", "react", "state", "sync", "tabs", "broadcast"], "author": "z0gSh1u", "license": "MIT", "peerDependencies": {"react": ">=18", "valtio": ">=2"}, "dependencies": {"broadcast-channel": "7", "lodash": "~4.17.x", "superjson": "~2.2.x"}, "devDependencies": {"@types/lodash": "~4.17.x", "@types/react": "18", "react": "18", "tsup": "8", "typescript": "~5.3.x", "valtio": "2", "wait-on": "^8.0.3"}, "files": ["dist"]}