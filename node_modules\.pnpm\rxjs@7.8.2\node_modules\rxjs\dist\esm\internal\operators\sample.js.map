{"version": 3, "file": "sample.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/sample.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AAEpD,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AACvC,OAAO,EAAE,IAAI,EAAE,MAAM,cAAc,CAAC;AACpC,OAAO,EAAE,wBAAwB,EAAE,MAAM,sBAAsB,CAAC;AA0ChE,MAAM,UAAU,MAAM,CAAI,QAA8B;IACtD,OAAO,OAAO,CAAC,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE;QACpC,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,IAAI,SAAS,GAAa,IAAI,CAAC;QAC/B,MAAM,CAAC,SAAS,CACd,wBAAwB,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE,EAAE;YAC7C,QAAQ,GAAG,IAAI,CAAC;YAChB,SAAS,GAAG,KAAK,CAAC;QACpB,CAAC,CAAC,CACH,CAAC;QACF,SAAS,CAAC,QAAQ,CAAC,CAAC,SAAS,CAC3B,wBAAwB,CACtB,UAAU,EACV,GAAG,EAAE;YACH,IAAI,QAAQ,EAAE;gBACZ,QAAQ,GAAG,KAAK,CAAC;gBACjB,MAAM,KAAK,GAAG,SAAU,CAAC;gBACzB,SAAS,GAAG,IAAI,CAAC;gBACjB,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACxB;QACH,CAAC,EACD,IAAI,CACL,CACF,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC"}