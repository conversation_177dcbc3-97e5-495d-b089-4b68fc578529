name: Build and deploy static content to Pages

on:
  push:
    branches: [master]
  workflow_dispatch:

permissions:
  contents: read
  pages: write
  id-token: write

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Use Node.js 20
        uses: actions/setup-node@v3
        with:
          node-version: 20
      - name: pnpm Install
        uses: pnpm/action-setup@v4
        with:
          version: 9
      - name: Build Bundle
        run: pnpm install && pnpm build && cd example && pnpm install && pnpm build
      - name: Archive Production Artifact
        uses: actions/upload-artifact@v4
        with:
          name: built-github-pages
          path: example/dist
          retention-days: 1

  deploy:
    needs: build
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    steps:
      - id: step1
        name: Download Artifact
        uses: actions/download-artifact@v4
        with:
          name: built-github-pages
      - name: Setup Pages
        uses: actions/configure-pages@v5
      - name: Upload Pages Artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: ${{ steps.step1.outputs.download-path }}
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
